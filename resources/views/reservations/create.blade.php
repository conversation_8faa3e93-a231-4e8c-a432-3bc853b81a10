@extends('layouts.admin')

@section('title', 'New Reservation - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">New</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- Page Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="bx bx-plus-circle me-2"></i>Create New Reservation
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.index') }}" class="btn btn-secondary btn-sm">
                            <i class="ti ti-arrow-left me-1"></i>Back to Reservations
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.store') }}" id="reservationForm">
                        @csrf

                        <div class="row gy-4">
                            <!-- Left Column: Field Selection, Utilities, and Cost Overview -->
                            <div class="col-xl-6">
                                <!-- Field Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Field Selection
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-rate2="{{ $field->night_hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            data-anochi="{{ $field->night_time_start }}"
                                                            {{ old('field_id', $selectedField?->id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }}
                                                            <!-- -
                                                                                                                                                                                                                                                                                                                                                Day rate XCG {{ number_format($field->hourly_rate, 2) }}/hr and
                                                                                                                                                                                                                                                                                                                                                Night rate XCG
                                                                                                                                                                                                                                                                                                                                                {{ number_format($field->night_hourly_rate, 2) }}/hr -->
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo"
                                                    class="alert alert-info {{ $selectedField ? '' : 'd-none' }}">
                                                    <p class="mb-1"><i class="ti ti-info-circle me-2"></i><strong>Field Information</strong></p>
                                                    <p class="mb-1"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $selectedField?->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1"><strong>Rates:</strong> Day XCG <span
                                                            id="fieldRate">{{ $selectedField ? number_format($selectedField->hourly_rate, 2) : '0.00' }}</span>/hr, Night XCG <span
                                                            id="fieldRate2">{{ $selectedField ? number_format($selectedField->night_hourly_rate, 2) : '0.00' }}</span>/hr
                                                    </p>
                                                    <p class="mb-0"><strong>Hours:</strong> <span
                                                            id="fieldHours">{{ $selectedField?->opening_time }} -
                                                            {{ $selectedField?->closing_time }}</span> | <strong>Duration:</strong> <span
                                                            id="fieldDuration">{{ $selectedField?->min_booking_hours }} -
                                                            {{ $selectedField?->max_booking_hours }}</span> hours</p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <label for="booking_date" class="form-label">Date <span
                                                        class="text-danger">*</span></label>
                                                <input type="date" name="booking_date" id="booking_date"
                                                    value="{{ old('booking_date', $selectedDate ?: date('Y-m-d')) }}"
                                                    min="{{ date('Y-m-d') }}" required
                                                    onchange="loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-control @error('booking_date') is-invalid @enderror">
                                                @error('booking_date')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Start Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="start_time" class="form-label">Start Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required disabled
                                                    onchange="updateEndTimeOptions(); calculateCost(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Start Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text text-muted" id="startTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a field first
                                                </div>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- End Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="end_time" class="form-label">End Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="end_time" id="end_time" required disabled
                                                    onchange="calculateCost(); checkUtilityPrerequisites();"
                                                    class="form-select @error('end_time') is-invalid @enderror">
                                                    <option value="">Select End Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text text-muted" id="endTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a start time first
                                                </div>
                                                @error('end_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Utility Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Utility Selection (Optional)
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>

                                                <!-- Utility Prerequisites Message -->
                                                <div id="utilityPrerequisitesMessage" class="alert alert-info mb-3">
                                                    {{-- <i class="ti ti-info-circle me-2"></i>
                                                    <strong>Complete booking details first</strong> 
                                                    Please select field, date, time, and duration before adding utilities. --}}
                                                </div>

                                                <!-- Utility Selection Section -->
                                                <div id="utilitySelectionSection" class="utility-section-disabled">
                                                    <div class="row g-2 align-items-center mb-3">
                                                        <div class="col-md-5">
                                                            <select id="utilitySelect" class="form-select" disabled>
                                                                <option value="">Select Utility</option>
                                                                @foreach ($utilities as $utility)
                                                                    <option value="{{ $utility->id }}"
                                                                        data-name="{{ $utility->name }}"
                                                                        data-rate="{{ $utility->hourly_rate }}">
                                                                        {{ $utility->name }} -
                                                                        XCG {{ number_format($utility->hourly_rate, 2) }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <input type="number" id="utilityQuantity"
                                                                class="form-control" min="1" step="1"
                                                                value="1" placeholder="Quantity" disabled>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button" class="btn btn-primary w-100"
                                                                onclick="addUtility()" id="addUtilityBtn"
                                                                disabled>Add</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <table class="table table-bordered" id="utilityTable">
                                                    <thead id="utilityTableHeader" class="d-none">
                                                        <tr>
                                                            <th>Utility</th>
                                                            <th>Quantity</th>
                                                            <th>Rate</th>
                                                            <th>Cost</th>
                                                            <th>Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Dynamic rows will be added here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                            <!-- end Utilities -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Total Cost Overview Section -->
                                <div class="card custom-card shadow-none border mb-4 d-none" id="costOverviewCard">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Total Cost Overview
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>

                                            <!-- Enhanced Cost Display with Detailed Breakdown -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost Breakdown</h6>

                                                    <!-- Field Cost Breakdown -->
                                                    <div id="fieldCostBreakdown" class="mb-2">
                                                        <div class="fw-semibold mb-1">Field Cost:</div>
                                                        <div id="dayNightBreakdown" class="fs-12 text-muted mb-1">
                                                            <!-- Day/Night breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Field Total: XCG <span
                                                                    id="fieldCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Utility Cost Breakdown -->
                                                    <div id="utilityCostBreakdown" class="mb-2 d-none">
                                                        <div class="fw-semibold mb-1">Utility Costs:</div>
                                                        <div id="utilityDetails" class="fs-12 text-muted mb-1">
                                                            <!-- Utility breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Utility Total: XCG <span
                                                                    id="utilityCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Total Cost -->
                                                    <div class="border-top pt-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="fw-bold">Total Cost:</span>
                                                            <span class="h5 mb-0 text-success">XCG <span
                                                                    id="totalCost">0.00</span></span>
                                                        </div>
                                                    </div>

                                                    <!-- Server-side calculation notice -->
                                                    <div class="fs-11 text-muted mt-1">
                                                        <i class="ti ti-shield-check me-1"></i>Costs calculated securely by
                                                        server
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" name="customer_name" id="customer_name" required
                                                    value="{{ old('customer_name') }}"
                                                    placeholder="Name (required)"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email') }}"
                                                    placeholder="Email (optional)"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone <span
                                                        class="text-danger">*</span></label>
                                                <input type="tel" name="customer_phone" id="customer_phone" required
                                                    value="{{ old('customer_phone') }}" 
                                                    placeholder="Phone number (required)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests') }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Reservation Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-default">
                                                    <h6 class="fw-semibold">FPMP Reservation Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Reservations are available from 8:00 AM to 10:00 PM</li>
                                                        <li> All reservations are automatically confirmed</li>
                                                        <li> Cancellations must be made at least 24 hours in advance</li>
                                                        <li> Modifications must be made at least 24 hours in advance</li>
                                                        <li> Please arrive 15 minutes before your scheduled time</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-end mt-4 pt-3 border-top">
                                    <a href="{{ route('reservations.index') }}" class="btn btn-secondary">
                                        <i class="ti ti-x me-1"></i>Cancel
                                    </a>
                                    <button type="submit" id="submitBtn" class="btn btn-primary">
                                        <i class="ti ti-check me-1"></i>Create Reservation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@push('styles')
    <style>
        /* Progressive Form Flow Styles */
        .utility-section-disabled {
            opacity: 0.6;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        .utility-section-enabled {
            opacity: 1;
            pointer-events: auto;
            transition: opacity 0.3s ease;
        }

        #utilityPrerequisitesMessage {
            transition: all 0.3s ease;
        }

        #utilityPrerequisitesMessage.d-none {
            opacity: 0;
            transform: translateY(-10px);
        }

        /* Progressive Field Activation Styles */
        .form-select:disabled,
        .form-control:disabled {
            background-color: #f8f9fa;
            opacity: 0.65;
            cursor: not-allowed;
        }

        .form-text.text-muted {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .form-text.text-muted i {
            color: #6c757d;
        }

        /* Form step indicators */
        .form-step-complete {
            position: relative;
        }

        .form-step-complete::after {
            content: '✓';
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #28a745;
            font-weight: bold;
            font-size: 16px;
        }

        /* Utility table header transition */
        #utilityTableHeader {
            transition: all 0.3s ease;
        }

        #utilityTableHeader.d-none {
            opacity: 0;
            transform: translateY(-10px);
        }
    </style>
@endpush

@push('scripts')
    <script>
        function updateFieldInfo() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const fieldInfo = document.getElementById('fieldInfo');

            if (selectedOption.value) {
                document.getElementById('fieldType').textContent = selectedOption.dataset.type || 'N/A';
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                document.getElementById('fieldRate2').textContent = parseFloat(selectedOption.dataset.rate2 || 0).toFixed(
                    2);
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (
                    selectedOption.dataset.closing || '22:00');
                document.getElementById('fieldDuration').textContent = (selectedOption.dataset.minHours || '1') + ' - ' + (
                    selectedOption.dataset.maxHours || '8') + ' hours';
                fieldInfo.classList.remove('d-none');
                calculateCost();
            } else {
                fieldInfo.classList.add('d-none');
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
            }
            checkUtilityPrerequisites(); // Check utility prerequisites when field changes
            enableProgressiveFields(); // Enable progressive field activation
        }



        // Performance optimization: debounce and request deduplication
        let costCalculationTimeout;
        let lastRequestData = null;
        let currentRequest = null;

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const rate = parseFloat(selectedOption.dataset.rate || 0);

            const fieldId = fieldSelect.value;
            const startTime = startTimeSelect.value;
            const endTime = endTimeSelect.value;

            if (!fieldId || !startTime || !endTime) {
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Calculate duration from start and end times
            const duration = calculateDurationFromTimes(startTime, endTime);
            if (duration <= 0) {
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Prepare utilities data for server calculation
            const utilitiesData = utilities.map(u => ({
                id: u.id,
                hours: u.quantity
            }));

            // Debug: Log cost calculation request
            console.log('Cost calculation triggered:', {
                fieldId,
                startTime,
                endTime,
                duration,
                utilities: utilitiesData
            });

            // Create request data for comparison
            const requestData = {
                field_id: fieldId,
                start_time: startTime,
                end_time: endTime,
                utilities: utilitiesData
            };

            // Check if request data has changed (avoid redundant requests)
            const requestDataString = JSON.stringify(requestData);
            if (lastRequestData === requestDataString) {
                return; // No change, skip request
            }

            // Cancel previous timeout
            if (costCalculationTimeout) {
                clearTimeout(costCalculationTimeout);
            }

            // Debounce the request (300ms delay)
            costCalculationTimeout = setTimeout(() => {
                // Cancel previous request if still pending
                if (currentRequest) {
                    currentRequest.abort();
                }

                // Store current request data
                lastRequestData = requestDataString;

                // Create AbortController for request cancellation
                const controller = new AbortController();
                currentRequest = controller;

                // Fetch cost calculation from server
                fetch(`{{ route('reservations.cost-estimate') }}`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        },
                        body: JSON.stringify(requestData),
                        signal: controller.signal
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Clear current request reference
                        currentRequest = null;

                        // Debug: Log server response
                        console.log('Server response received:', {
                            total_cost: data.total_cost,
                            utility_breakdown_count: data.utility_breakdown ? data.utility_breakdown
                                .length : 0
                        });

                        if (data.error) {
                            console.error('Cost calculation error:', data.error);
                            return;
                        }

                        // Update UI with server-calculated detailed breakdown
                        const totalCost = parseFloat(data.total_cost || 0);
                        document.getElementById('totalCost').textContent = totalCost.toFixed(2);

                        // Update field cost breakdown
                        const fieldCost = parseFloat(data.field_cost || data.subtotal || data.total_cost || 0);
                        document.getElementById('fieldCost').textContent = fieldCost.toFixed(2);

                        // Update day/night rate breakdown
                        const dayNightBreakdown = document.getElementById('dayNightBreakdown');
                        if (data.rate_breakdown && (data.rate_breakdown.day_hours > 0 || data.rate_breakdown
                                .night_hours > 0)) {
                            let breakdownHtml = '';
                            if (data.rate_breakdown.day_hours > 0) {
                                const dayRate = parseFloat(data.hourly_rate || 0);
                                const dayCost = parseFloat(data.rate_breakdown.day_cost || 0);
                                breakdownHtml +=
                                    `Day Rate: ${data.rate_breakdown.day_hours} hours × XCG ${dayRate.toFixed(2)} = XCG ${dayCost.toFixed(2)}<br>`;
                            }
                            if (data.rate_breakdown.night_hours > 0) {
                                const nightRate = parseFloat(data.night_hourly_rate || 0);
                                const nightCost = parseFloat(data.rate_breakdown.night_cost || 0);
                                breakdownHtml +=
                                    `Night Rate: ${data.rate_breakdown.night_hours} hours × XCG ${nightRate.toFixed(2)} = XCG ${nightCost.toFixed(2)}`;
                            }
                            dayNightBreakdown.innerHTML = breakdownHtml;
                        } else {
                            // Simple rate display for fields without night rates
                            dayNightBreakdown.innerHTML =
                                `${duration} hours × XCG ${rate.toFixed(2)} = XCG ${fieldCost.toFixed(2)}`;
                        }

                        // Update utility cost breakdown
                        const utilityCostBreakdown = document.getElementById('utilityCostBreakdown');
                        const utilityDetails = document.getElementById('utilityDetails');
                        const utilityCostSpan = document.getElementById('utilityCost');

                        if (data.utility_breakdown && data.utility_breakdown.length > 0) {
                            let utilityHtml = '';
                            let totalUtilityCost = 0;

                            data.utility_breakdown.forEach(utility => {
                                const utilityRate = parseFloat(utility.rate || 0);
                                const utilityCost = parseFloat(utility.cost || 0);
                                utilityHtml +=
                                    `${utility.name}: ${utility.hours} × XCG ${utilityRate.toFixed(2)} = XCG ${utilityCost.toFixed(2)}<br>`;
                                totalUtilityCost += utilityCost;

                                // Update utility costs in table with multiple methods
                                // Method 1: Direct cost cell ID (most reliable)
                                const costCellById = document.getElementById(
                                    `utility-cost-${utility.utility_id}`);

                                if (costCellById) {
                                    costCellById.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                                    console.log(
                                        `✅ Utility cost updated: ${utility.name} = XCG ${utilityCost.toFixed(2)}`
                                    );
                                    return; // Success, no need for fallback methods
                                }

                                // Method 2: Row data attribute
                                let utilityRow = document.querySelector(
                                    `#utilityTable tbody tr[data-utility-id="${utility.utility_id}"]`
                                );

                                // Method 3: Try finding by hidden input name
                                if (!utilityRow) {
                                    const rows = document.querySelectorAll('#utilityTable tbody tr');
                                    rows.forEach(row => {
                                        const hiddenInput = row.querySelector(
                                            `input[name="utilities[${utility.utility_id}][id]"]`
                                        );
                                        if (hiddenInput) {
                                            utilityRow = row;
                                        }
                                    });
                                }

                                // Method 4: Try finding by utility name (less reliable but better than nothing)
                                if (!utilityRow) {
                                    const rows = document.querySelectorAll('#utilityTable tbody tr');
                                    rows.forEach(row => {
                                        const nameCell = row.cells[0];
                                        if (nameCell && nameCell.textContent.trim() === utility
                                            .name) {
                                            utilityRow = row;
                                        }
                                    });
                                }

                                if (utilityRow) {
                                    const costCell = utilityRow.cells[
                                        3
                                    ]; // Cost column (0: name, 1: quantity, 2: rate, 3: cost, 4: action)
                                    if (costCell) {
                                        costCell.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                                        console.log(
                                            `✅ Utility cost updated (fallback): ${utility.name} = XCG ${utilityCost.toFixed(2)}`
                                        );
                                    }
                                } else {
                                    console.warn(
                                        `⚠️ Could not find utility row for ${utility.name} (ID: ${utility.utility_id})`
                                    );
                                }
                            });

                            utilityDetails.innerHTML = utilityHtml;
                            utilityCostSpan.textContent = totalUtilityCost.toFixed(2);
                            utilityCostBreakdown.classList.remove('d-none');
                        } else {
                            utilityCostBreakdown.classList.add('d-none');
                            utilityCostSpan.textContent = '0.00';
                        }

                        // Ensure success styling and show cost overview card
                        const costDisplay = document.getElementById('costDisplay');
                        costDisplay.className = 'alert alert-success d-block';
                        costDisplay.classList.remove('d-none');
                        document.getElementById('costOverviewCard').classList.remove('d-none');
                    })
                    .catch(error => {
                        // Clear current request reference
                        currentRequest = null;

                        // Don't log aborted requests as errors
                        if (error.name === 'AbortError') {
                            return;
                        }

                        console.error('Error calculating cost:', error);
                        // Show error state instead of client-side calculation
                        document.getElementById('totalCost').textContent = 'Error';
                        document.getElementById('fieldCost').textContent = 'Error';
                        document.getElementById('costDisplay').classList.remove('d-none');
                        document.getElementById('costOverviewCard').classList.remove('d-none');

                        // Optionally show user-friendly error message
                        const costDisplay = document.getElementById('costDisplay');
                        costDisplay.className = 'alert alert-warning d-block';
                        costDisplay.innerHTML = `
                        <h6 class="fw-semibold">Cost Calculation Unavailable</h6>
                        <p class="mb-0">Unable to calculate cost at this time. Please try again or contact support.</p>
                    `;
                    });
            }, 300); // 300ms debounce delay
        }

        function loadAvailability() {
            const fieldId = document.getElementById('field_id').value;
            const date = document.getElementById('booking_date').value;

            if (!fieldId || !date) {
                return;
            }

            // Load available start time slots (using minimum duration for initial availability)
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const minDuration = parseFloat(selectedOption.dataset.minHours || 0.5);

            fetch(`{{ route('reservations.check-availability') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        date: date,
                        duration_hours: minDuration
                    })
                })
                .then(response => response.json())
                .then(data => {
                    updateTimeSlots(data.slots || []);
                })
                .catch(error => {
                    console.error('Error loading availability:', error);
                });
        }

        function updateTimeSlots(slots) {
            const startTimeSelect = document.getElementById('start_time');
            const currentStartValue = startTimeSelect.value || '{{ $selectedTime ?? '' }}';

            // Clear existing options for start time
            startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';

            // Add available start time slots
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                // Show only the time value for start time (e.g., "13:00")
                option.textContent = slot.start_time;
                if (slot.start_time === currentStartValue) {
                    option.selected = true;
                }
                startTimeSelect.appendChild(option);
            });

            // If a start time was pre-selected, update end time options
            if (currentStartValue) {
                updateEndTimeOptions();
            }

            // Check utility prerequisites after time slots are updated
            checkUtilityPrerequisites();

            // Trigger cost calculation if both times were pre-selected
            if (currentStartValue && document.getElementById('end_time').value) {
                calculateCost();
            }
        }

        // Progressive Form Flow Functions
        function checkUtilityPrerequisites() {
            const fieldId = document.getElementById('field_id').value;
            const bookingDate = document.getElementById('booking_date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            const allRequiredFieldsSelected = fieldId && bookingDate && startTime && endTime;

            const utilitySelect = document.getElementById('utilitySelect');
            const utilityQuantity = document.getElementById('utilityQuantity');
            const addUtilityBtn = document.getElementById('addUtilityBtn');
            const utilitySection = document.getElementById('utilitySelectionSection');
            const prerequisitesMessage = document.getElementById('utilityPrerequisitesMessage');

            if (allRequiredFieldsSelected) {
                // Enable utilities section
                utilitySelect.disabled = false;
                utilityQuantity.disabled = false;
                addUtilityBtn.disabled = false;
                utilitySection.className = 'utility-section-enabled';
                prerequisitesMessage.classList.add('d-none');

                // Add visual indicators to completed fields
                addFormStepIndicator('field_id');
                addFormStepIndicator('booking_date');
                addFormStepIndicator('start_time');
                addFormStepIndicator('end_time');
            } else {
                // Disable utilities section
                utilitySelect.disabled = true;
                utilityQuantity.disabled = true;
                addUtilityBtn.disabled = true;
                utilitySection.className = 'utility-section-disabled';
                prerequisitesMessage.classList.remove('d-none');

                // Update prerequisites message with specific missing fields
                updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime);

                // Remove visual indicators from incomplete fields
                removeFormStepIndicator('field_id');
                removeFormStepIndicator('booking_date');
                removeFormStepIndicator('start_time');
                removeFormStepIndicator('end_time');
            }
        }

        function updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime) {
            const missingFields = [];
            if (!fieldId) missingFields.push('field');
            if (!bookingDate) missingFields.push('date');
            if (!startTime) missingFields.push('start time');
            if (!endTime) missingFields.push('end time');

            const message = document.getElementById('utilityPrerequisitesMessage');
            if (missingFields.length > 0) {
                const fieldList = missingFields.join(', ');
                message.innerHTML = `
                    <i class="ti ti-info-circle me-2"></i>
                    <strong>Complete booking details first</strong><br>
                    Please select ${fieldList} before adding utilities.
                `;
            }
        }

        function addFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                field.classList.add('form-step-complete');
            }
        }

        function removeFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('form-step-complete');
            }
        }

        // Progressive Field Activation Function
        function enableProgressiveFields() {
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const startTimeHelp = document.getElementById('startTimeHelp');
            const endTimeHelp = document.getElementById('endTimeHelp');

            const fieldSelected = fieldSelect.value;
            const startTimeSelected = startTimeSelect.value;

            // Enable/disable start time based on field selection
            if (fieldSelected) {
                startTimeSelect.disabled = false;
                startTimeHelp.innerHTML = '';
            } else {
                startTimeSelect.disabled = true;
                startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';
                startTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a field first';

                // Also disable end time if field is not selected
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                return;
            }

            // Enable/disable end time based on start time selection
            if (startTimeSelected) {
                updateEndTimeOptions();
            } else {
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
            }
        }

        // New functions for start/end time logic
        function updateEndTimeOptions() {
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const endTimeHelp = document.getElementById('endTimeHelp');
            const fieldSelect = document.getElementById('field_id');
            const dateInput = document.getElementById('booking_date');

            const startTime = startTimeSelect.value;
            const selectedField = fieldSelect.options[fieldSelect.selectedIndex];
            const date = dateInput.value;

            if (!startTime || !selectedField.value || !date) {
                // Disable end time if no start time selected
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';
                return;
            }

            // Show loading state
            endTimeSelect.disabled = true;
            endTimeSelect.innerHTML = '<option value="">Loading available times...</option>';
            endTimeHelp.innerHTML = '<i class="ti ti-loader me-1"></i>Checking availability...';

            // Fetch available end times from server
            fetch(`{{ route('reservations.available-end-times') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field_id: selectedField.value,
                    date: date,
                    start_time: startTime
                })
            })
            .then(response => response.json())
            .then(data => {
                // Enable end time dropdown
                endTimeSelect.disabled = false;
                endTimeHelp.innerHTML = '';

                if (data.success && data.end_times.length > 0) {
                    // Clear and populate end time options
                    endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                    data.end_times.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.text;
                        endTimeSelect.appendChild(optionElement);
                    });
                } else {
                    // No available end times
                    endTimeSelect.innerHTML = '<option value="">No available end times</option>';
                    endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-warning"></i>No available end times for this start time';
                }
            })
            .catch(error => {
                console.error('Error fetching available end times:', error);
                endTimeSelect.disabled = false;
                endTimeSelect.innerHTML = '<option value="">Error loading times</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-danger"></i>Error loading available times';
            });
        }



        function calculateDurationFromTimes(startTime, endTime) {
            if (!startTime || !endTime) return 0;

            const startDate = new Date(`2000-01-01 ${startTime}`);
            const endDate = new Date(`2000-01-01 ${endTime}`);

            if (endDate <= startDate) return 0;

            return (endDate - startDate) / (1000 * 60 * 60); // Duration in hours
        }

        function formatTimeDisplay(timeString) {
            const time = new Date(`2000-01-01 ${timeString}`);
            return time.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        function formatDuration(hours) {
            if (hours === 0.5) return '30 min';
            if (hours === 1) return '1 hour';
            if (hours % 1 === 0) return `${hours} hours`;
            return `${hours} hours`;
        }

        // Function to handle calendar integration
        function setTimeFromCalendar(startTime, defaultDuration = 1) {
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');

            // Set start time if available in options
            if (startTimeSelect.querySelector(`option[value="${startTime}"]`)) {
                startTimeSelect.value = startTime;

                // Update end time options
                updateEndTimeOptions();

                // Calculate default end time
                const startDate = new Date(`2000-01-01 ${startTime}`);
                const endDate = new Date(startDate.getTime() + (defaultDuration * 60 * 60 * 1000));
                const endTime = endDate.toTimeString().substring(0, 5);

                // Set end time if available in options
                if (endTimeSelect.querySelector(`option[value="${endTime}"]`)) {
                    endTimeSelect.value = endTime;

                    // Trigger cost calculation
                    calculateCost();
                    checkUtilityPrerequisites();
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            updateFieldInfo();

            // Initialize progressive field activation
            enableProgressiveFields();

            // Load availability to populate time slots
            loadAvailability();

            // Initialize utility prerequisites check
            checkUtilityPrerequisites();

            // If we have a pre-selected time from URL, set it up with default duration
            const preSelectedTime = '{{ $selectedTime ?? '' }}';
            if (preSelectedTime) {
                // Delay to ensure time slots are loaded first
                setTimeout(() => {
                    setTimeFromCalendar(preSelectedTime, 1); // Default 1 hour duration
                }, 500);
            }
        });
    </script>

    <script>
        /////////////////////////////////// di utilities
        let utilities = [];

        function addUtility() {
            const select = document.getElementById('utilitySelect');
            const quantityInput = document.getElementById('utilityQuantity');
            const tableBody = document.querySelector('#utilityTable tbody');
            const tableHeader = document.getElementById('utilityTableHeader');

            const id = select.value;
            const name = select.options[select.selectedIndex].dataset.name;
            const rate = parseFloat(select.options[select.selectedIndex].dataset.rate || 0);
            const quantity = parseInt(quantityInput.value || 1);

            if (!id || quantity <= 0 || !Number.isInteger(quantity)) {
                alert("Please select a utility and enter a valid whole number quantity.");
                return;
            }

            if (utilities.find(u => u.id === id)) {
                alert("This utility is already added.");
                return;
            }

            utilities.push({
                id,
                name,
                rate,
                quantity
            });

            const row = document.createElement('tr');
            row.setAttribute('data-utility-id', id);
            row.id = `utility-row-${id}`; // Add unique ID for more reliable selection
            row.innerHTML = `
        <td>${name}</td>
        <td>${quantity}</td>
        <td>XCG ${rate.toFixed(2)}</td>
        <td id="utility-cost-${id}"><span class="text-muted">Calculating...</span></td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
        </td>
        <input type="hidden" name="utilities[${id}][id]" value="${id}">
        <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
    `;

            tableBody.appendChild(row);

            // Show table header when first utility is added
            if (utilities.length === 1) {
                tableHeader.classList.remove('d-none');
            }

            console.log(`Utility added: ${name} (${quantity} hours)`);

            // Reset
            select.selectedIndex = 0;
            quantityInput.value = 1;

            // Trigger cost calculation to update utility costs
            calculateCost();
        }

        function removeUtility(id, btn) {
            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();

            // Hide table header when no utilities remain
            const tableHeader = document.getElementById('utilityTableHeader');
            if (utilities.length === 0) {
                tableHeader.classList.add('d-none');
            }

            calculateCost();
        }
    </script>
@endpush
